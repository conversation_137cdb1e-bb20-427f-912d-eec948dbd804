using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;

namespace ZeroDateStrat.Utils;

public interface IRiskManager
{
    Task<bool> ValidateSignalAsync(TradingSignal signal);
    Task<decimal> CalculatePositionSizeAsync(TradingSignal signal);
    Task<decimal> GetDailyPnLAsync();
    Task<bool> CheckAccountLimitsAsync();
    Task<decimal> GetMaxRiskPerTradeAsync();
}

public class RiskManager : IRiskManager
{
    private readonly ILogger<RiskManager> _logger;
    private readonly IConfiguration _configuration;
    private readonly IAlpacaService _alpacaService;

    public RiskManager(
        ILogger<RiskManager> logger,
        IConfiguration configuration,
        IAlpacaService alpacaService)
    {
        _logger = logger;
        _configuration = configuration;
        _alpacaService = alpacaService;
    }

    public async Task<bool> ValidateSignalAsync(TradingSignal signal)
    {
        try
        {
            // Check if signal is valid
            if (!signal.IsValid)
            {
                _logger.LogWarning($"Signal {signal.Id} is not valid");
                return false;
            }

            // Check confidence threshold
            var minConfidence = 0.6m;
            if (signal.Confidence < minConfidence)
            {
                _logger.LogWarning($"Signal {signal.Id} confidence {signal.Confidence:P} below threshold {minConfidence:P}");
                return false;
            }

            // Check risk-reward ratio
            var minRiskReward = 0.3m;
            if (signal.RiskRewardRatio < minRiskReward)
            {
                _logger.LogWarning($"Signal {signal.Id} risk-reward ratio {signal.RiskRewardRatio:F2} below threshold {minRiskReward:F2}");
                return false;
            }

            // Check maximum loss per trade
            var maxRiskPerTrade = await GetMaxRiskPerTradeAsync();
            if (signal.MaxLoss > maxRiskPerTrade)
            {
                _logger.LogWarning($"Signal {signal.Id} max loss {signal.MaxLoss:C} exceeds per-trade limit {maxRiskPerTrade:C}");
                return false;
            }

            // Check account limits
            if (!await CheckAccountLimitsAsync())
            {
                _logger.LogWarning("Account limits exceeded");
                return false;
            }

            // Check if we already have too many positions in the same underlying
            var maxPositionsPerUnderlying = 3;
            var existingPositions = await GetExistingPositionsForSymbol(signal.UnderlyingSymbol);
            if (existingPositions >= maxPositionsPerUnderlying)
            {
                _logger.LogWarning($"Too many existing positions for {signal.UnderlyingSymbol}: {existingPositions}");
                return false;
            }

            // Check time to expiration (should be 0 for 0 DTE)
            if (signal.ExpirationDate.Date != DateTime.Today)
            {
                _logger.LogWarning($"Signal {signal.Id} is not 0 DTE: expires {signal.ExpirationDate:yyyy-MM-dd}");
                return false;
            }

            // Check market hours
            if (!IsMarketHours())
            {
                _logger.LogWarning("Outside market hours");
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error validating signal {signal.Id}");
            return false;
        }
    }

    public async Task<decimal> CalculatePositionSizeAsync(TradingSignal signal)
    {
        try
        {
            var account = await _alpacaService.GetAccountAsync();
            if (account == null)
                return 0;

            var buyingPower = account.BuyingPower;
            var riskPerTrade = _configuration.GetValue<decimal>("Trading:RiskPerTrade", 0.02m);
            var maxPositionSize = _configuration.GetValue<decimal>("Trading:MaxPositionSize", 10000);

            // Calculate position size based on risk per trade
            var accountValue = account.Equity;
            var maxRiskAmount = accountValue * riskPerTrade;
            
            // Position size based on maximum loss
            var positionSizeByRisk = signal.MaxLoss > 0 ? maxRiskAmount / signal.MaxLoss : 0;
            
            // Position size based on buying power (conservative approach)
            var positionSizeByBuyingPower = Math.Min(buyingPower * 0.1m, maxPositionSize) / 
                                          (signal.MaxLoss > 0 ? signal.MaxLoss : 1000);

            // Take the smaller of the two
            var positionSize = Math.Min(positionSizeByRisk, positionSizeByBuyingPower);
            
            // Ensure minimum position size of 1
            positionSize = Math.Max(1, Math.Floor(positionSize));

            _logger.LogInformation($"Calculated position size: {positionSize} for signal {signal.Id}");
            return positionSize;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error calculating position size for signal {signal.Id}");
            return 0;
        }
    }

    public async Task<decimal> GetDailyPnLAsync()
    {
        try
        {
            // In a real implementation, you'd calculate this from today's trades
            // For now, return a placeholder
            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting daily P&L");
            return 0;
        }
    }

    public async Task<bool> CheckAccountLimitsAsync()
    {
        try
        {
            var account = await _alpacaService.GetAccountAsync();
            if (account == null)
                return false;

            // Check if account is restricted
            if (account.TradingBlocked)
            {
                _logger.LogWarning("Trading is blocked on account");
                return false;
            }

            // Check buying power
            var minBuyingPower = 1000m;
            if (account.BuyingPower < minBuyingPower)
            {
                _logger.LogWarning($"Insufficient buying power: {account.BuyingPower:C} < {minBuyingPower:C}");
                return false;
            }

            // Check day trading buying power (for pattern day traders)
            if (account.DayTradingBuyingPower < minBuyingPower)
            {
                _logger.LogWarning($"Insufficient day trading buying power: {account.DayTradingBuyingPower:C}");
                return false;
            }

            // Check if account is flagged as pattern day trader and has sufficient equity
            if (account.PatternDayTrader && account.Equity < 25000)
            {
                _logger.LogWarning("Pattern day trader with insufficient equity");
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking account limits");
            return false;
        }
    }

    public async Task<decimal> GetMaxRiskPerTradeAsync()
    {
        try
        {
            var account = await _alpacaService.GetAccountAsync();
            if (account == null)
                return 0;

            var riskPerTrade = _configuration.GetValue<decimal>("Trading:RiskPerTrade", 0.02m);
            return account.Equity * riskPerTrade;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting max risk per trade");
            return 0;
        }
    }

    private async Task<int> GetExistingPositionsForSymbol(string symbol)
    {
        try
        {
            var positions = await _alpacaService.GetPositionsAsync();
            return positions.Count(p => p.Symbol.StartsWith(symbol));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error getting existing positions for {symbol}");
            return 0;
        }
    }

    private bool IsMarketHours()
    {
        var now = DateTime.Now;
        var tradingStart = TimeSpan.Parse(_configuration["Trading:TradingStartTime"] ?? "09:30:00");
        var tradingEnd = TimeSpan.Parse(_configuration["Trading:TradingEndTime"] ?? "15:30:00");

        return now.DayOfWeek != DayOfWeek.Saturday && 
               now.DayOfWeek != DayOfWeek.Sunday &&
               now.TimeOfDay >= tradingStart && 
               now.TimeOfDay <= tradingEnd;
    }
}
