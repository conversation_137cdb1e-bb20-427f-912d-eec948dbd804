using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

public interface IOptionsScanner
{
    Task<List<OptionChain>> ScanForZeroDteOptionsAsync(List<string> symbols);
    Task<List<TradingSignal>> FindTradingOpportunitiesAsync(List<OptionChain> optionChains);
}

public class OptionsScanner : IOptionsScanner
{
    private readonly ILogger<OptionsScanner> _logger;
    private readonly IConfiguration _configuration;
    private readonly IAlpacaService _alpacaService;

    public OptionsScanner(
        ILogger<OptionsScanner> logger,
        IConfiguration configuration,
        IAlpacaService alpacaService)
    {
        _logger = logger;
        _configuration = configuration;
        _alpacaService = alpacaService;
    }

    public async Task<List<OptionChain>> ScanForZeroDteOptionsAsync(List<string> symbols)
    {
        var optionChains = new List<OptionChain>();
        var today = DateTime.Today;

        foreach (var symbol in symbols)
        {
            try
            {
                _logger.LogInformation($"Scanning 0 DTE options for {symbol}");

                var currentPrice = await _alpacaService.GetCurrentPriceAsync(symbol);
                if (currentPrice <= 0)
                {
                    _logger.LogWarning($"Could not get current price for {symbol}");
                    continue;
                }

                var options = await _alpacaService.GetOptionChainAsync(symbol, today);
                if (!options.Any())
                {
                    _logger.LogInformation($"No 0 DTE options found for {symbol}");
                    continue;
                }

                var optionChain = new OptionChain
                {
                    UnderlyingSymbol = symbol,
                    UnderlyingPrice = currentPrice,
                    ExpirationDate = today,
                    LastUpdated = DateTime.UtcNow
                };

                // Separate calls and puts
                optionChain.Calls = options.Where(o => o.OptionType == OptionType.Call).ToList();
                optionChain.Puts = options.Where(o => o.OptionType == OptionType.Put).ToList();

                // Filter for liquid options
                optionChain.Calls = FilterLiquidOptions(optionChain.Calls);
                optionChain.Puts = FilterLiquidOptions(optionChain.Puts);

                if (optionChain.Calls.Any() || optionChain.Puts.Any())
                {
                    optionChains.Add(optionChain);
                    _logger.LogInformation($"Found {optionChain.Calls.Count} calls and {optionChain.Puts.Count} puts for {symbol}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error scanning options for {symbol}");
            }
        }

        return optionChains;
    }

    public async Task<List<TradingSignal>> FindTradingOpportunitiesAsync(List<OptionChain> optionChains)
    {
        var signals = new List<TradingSignal>();

        foreach (var chain in optionChains)
        {
            try
            {
                // Look for Iron Condor opportunities
                var ironCondorSignals = await FindIronCondorOpportunities(chain);
                signals.AddRange(ironCondorSignals);

                // Look for Strangle opportunities
                var strangleSignals = await FindStrangleOpportunities(chain);
                signals.AddRange(strangleSignals);

                // Look for single option opportunities
                var singleOptionSignals = await FindSingleOptionOpportunities(chain);
                signals.AddRange(singleOptionSignals);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error finding opportunities for {chain.UnderlyingSymbol}");
            }
        }

        // Sort by confidence and expected profit
        return signals.Where(s => s.IsValid)
                     .OrderByDescending(s => s.Confidence)
                     .ThenByDescending(s => s.RiskRewardRatio)
                     .ToList();
    }

    private List<OptionContract> FilterLiquidOptions(List<OptionContract> options)
    {
        var minPrice = _configuration.GetValue<decimal>("Trading:MinOptionPrice", 0.05m);
        var maxPrice = _configuration.GetValue<decimal>("Trading:MaxOptionPrice", 5.00m);

        return options.Where(o => 
            o.IsLiquid && 
            o.MidPrice >= minPrice && 
            o.MidPrice <= maxPrice &&
            o.IsZeroDte)
            .ToList();
    }

    private async Task<List<TradingSignal>> FindIronCondorOpportunities(OptionChain chain)
    {
        var signals = new List<TradingSignal>();
        
        if (!_configuration.GetValue<bool>("Strategies:IronCondor:Enabled", true))
            return signals;

        var wingWidth = _configuration.GetValue<decimal>("Strategies:IronCondor:WingWidth", 5);
        var minCredit = _configuration.GetValue<decimal>("Strategies:IronCondor:MinCredit", 0.20m);

        try
        {
            var atmStrike = Math.Round(chain.UnderlyingPrice / 5) * 5; // Round to nearest $5

            // Find strikes for iron condor
            var putStrike1 = atmStrike - (wingWidth * 2); // Short put
            var putStrike2 = putStrike1 - wingWidth;      // Long put
            var callStrike1 = atmStrike + (wingWidth * 2); // Short call
            var callStrike2 = callStrike1 + wingWidth;     // Long call

            var shortPut = chain.GetPutByStrike(putStrike1);
            var longPut = chain.GetPutByStrike(putStrike2);
            var shortCall = chain.GetCallByStrike(callStrike1);
            var longCall = chain.GetCallByStrike(callStrike2);

            if (shortPut != null && longPut != null && shortCall != null && longCall != null)
            {
                var credit = (shortPut.MidPrice + shortCall.MidPrice) - (longPut.MidPrice + longCall.MidPrice);
                var maxLoss = wingWidth - credit;

                if (credit >= minCredit && maxLoss > 0)
                {
                    var signal = new TradingSignal
                    {
                        Strategy = "IronCondor",
                        UnderlyingSymbol = chain.UnderlyingSymbol,
                        Type = SignalType.IronCondor,
                        ExpirationDate = chain.ExpirationDate,
                        ExpectedProfit = credit * 0.5m, // Target 50% profit
                        MaxLoss = maxLoss,
                        Confidence = CalculateIronCondorConfidence(chain, credit, maxLoss),
                        Reason = $"Iron Condor: Credit={credit:C2}, MaxLoss={maxLoss:C2}",
                        Legs = new List<OptionLeg>
                        {
                            new() { Symbol = shortPut.Symbol, OptionType = OptionType.Put, StrikePrice = putStrike1, Side = OrderSide.Sell, Quantity = 1, Price = shortPut.MidPrice },
                            new() { Symbol = longPut.Symbol, OptionType = OptionType.Put, StrikePrice = putStrike2, Side = OrderSide.Buy, Quantity = 1, Price = longPut.MidPrice },
                            new() { Symbol = shortCall.Symbol, OptionType = OptionType.Call, StrikePrice = callStrike1, Side = OrderSide.Sell, Quantity = 1, Price = shortCall.MidPrice },
                            new() { Symbol = longCall.Symbol, OptionType = OptionType.Call, StrikePrice = callStrike2, Side = OrderSide.Buy, Quantity = 1, Price = longCall.MidPrice }
                        }
                    };

                    signals.Add(signal);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error finding iron condor opportunities for {chain.UnderlyingSymbol}");
        }

        return signals;
    }

    private async Task<List<TradingSignal>> FindStrangleOpportunities(OptionChain chain)
    {
        var signals = new List<TradingSignal>();
        
        if (!_configuration.GetValue<bool>("Strategies:Strangle:Enabled", true))
            return signals;

        var minCredit = _configuration.GetValue<decimal>("Strategies:Strangle:MinCredit", 0.30m);
        var minDelta = _configuration.GetValue<decimal>("Strategies:Strangle:DeltaRange:Min", 0.15m);
        var maxDelta = _configuration.GetValue<decimal>("Strategies:Strangle:DeltaRange:Max", 0.25m);

        try
        {
            // Find options with appropriate delta
            var suitablePuts = chain.Puts.Where(p => Math.Abs(p.Delta) >= minDelta && Math.Abs(p.Delta) <= maxDelta).ToList();
            var suitableCalls = chain.Calls.Where(c => c.Delta >= minDelta && c.Delta <= maxDelta).ToList();

            foreach (var put in suitablePuts)
            {
                foreach (var call in suitableCalls)
                {
                    var credit = put.MidPrice + call.MidPrice;
                    
                    if (credit >= minCredit)
                    {
                        var signal = new TradingSignal
                        {
                            Strategy = "Strangle",
                            UnderlyingSymbol = chain.UnderlyingSymbol,
                            Type = SignalType.Strangle,
                            ExpirationDate = chain.ExpirationDate,
                            ExpectedProfit = credit * 0.5m,
                            MaxLoss = decimal.MaxValue, // Undefined for short strangle
                            Confidence = CalculateStrangleConfidence(put, call, credit),
                            Reason = $"Short Strangle: Credit={credit:C2}, Put Delta={put.Delta:F2}, Call Delta={call.Delta:F2}",
                            Legs = new List<OptionLeg>
                            {
                                new() { Symbol = put.Symbol, OptionType = OptionType.Put, StrikePrice = put.StrikePrice, Side = OrderSide.Sell, Quantity = 1, Price = put.MidPrice },
                                new() { Symbol = call.Symbol, OptionType = OptionType.Call, StrikePrice = call.StrikePrice, Side = OrderSide.Sell, Quantity = 1, Price = call.MidPrice }
                            }
                        };

                        signals.Add(signal);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error finding strangle opportunities for {chain.UnderlyingSymbol}");
        }

        return signals.Take(3).ToList(); // Limit to top 3 strangle opportunities
    }

    private async Task<List<TradingSignal>> FindSingleOptionOpportunities(OptionChain chain)
    {
        var signals = new List<TradingSignal>();

        // This is a placeholder for single option strategies
        // You could implement momentum-based or mean reversion strategies here

        return signals;
    }

    private decimal CalculateIronCondorConfidence(OptionChain chain, decimal credit, decimal maxLoss)
    {
        var riskReward = credit / maxLoss;
        var liquidityScore = (chain.Calls.Average(c => c.Volume) + chain.Puts.Average(p => p.Volume)) / 100;
        var timeDecayBenefit = 0.8m; // 0 DTE has high time decay

        return Math.Min(0.95m, (riskReward * 0.4m) + (liquidityScore * 0.3m) + (timeDecayBenefit * 0.3m));
    }

    private decimal CalculateStrangleConfidence(OptionContract put, OptionContract call, decimal credit)
    {
        var liquidityScore = Math.Min(1.0m, (put.Volume + call.Volume) / 100);
        var deltaBalance = 1 - Math.Abs(Math.Abs(put.Delta) - call.Delta);
        var creditScore = Math.Min(1.0m, credit / 1.0m); // Normalize to $1.00

        return Math.Min(0.95m, (liquidityScore * 0.4m) + (deltaBalance * 0.3m) + (creditScore * 0.3m));
    }
}
