using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;
using ZeroDateStrat.Utils;

namespace ZeroDateStrat.Tests;

public class BasicTests
{
    private readonly Mock<ILogger<RiskManager>> _mockLogger;
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly Mock<IAlpacaService> _mockAlpacaService;

    public BasicTests()
    {
        _mockLogger = new Mock<ILogger<RiskManager>>();
        _mockConfiguration = new Mock<IConfiguration>();
        _mockAlpacaService = new Mock<IAlpacaService>();
    }

    public void TestOptionContractProperties()
    {
        // Arrange
        var option = new OptionContract
        {
            Symbol = "SPY240315C00500000",
            UnderlyingSymbol = "SPY",
            ExpirationDate = DateTime.Today,
            StrikePrice = 500m,
            OptionType = OptionType.Call,
            Bid = 1.50m,
            Ask = 1.60m,
            Volume = 100,
            OpenInterest = 500
        };

        // Act & Assert
        Console.WriteLine($"Mid Price: {option.MidPrice}"); // Should be 1.55
        Console.WriteLine($"Spread: {option.Spread}"); // Should be 0.10
        Console.WriteLine($"Is Zero DTE: {option.IsZeroDte}"); // Should be true
        Console.WriteLine($"Is Liquid: {option.IsLiquid}"); // Should be true
    }

    public void TestTradingSignalValidation()
    {
        // Arrange
        var signal = new TradingSignal
        {
            Strategy = "IronCondor",
            UnderlyingSymbol = "SPY",
            Type = SignalType.IronCondor,
            Confidence = 0.75m,
            ExpectedProfit = 50m,
            MaxLoss = 150m,
            Legs = new List<OptionLeg>
            {
                new() { Symbol = "SPY240315P00495000", OptionType = OptionType.Put, Side = OrderSide.Sell, Quantity = 1 },
                new() { Symbol = "SPY240315P00490000", OptionType = OptionType.Put, Side = OrderSide.Buy, Quantity = 1 },
                new() { Symbol = "SPY240315C00505000", OptionType = OptionType.Call, Side = OrderSide.Sell, Quantity = 1 },
                new() { Symbol = "SPY240315C00510000", OptionType = OptionType.Call, Side = OrderSide.Buy, Quantity = 1 }
            }
        };

        // Act & Assert
        Console.WriteLine($"Risk/Reward Ratio: {signal.RiskRewardRatio:F2}"); // Should be 0.33
        Console.WriteLine($"Is Valid: {signal.IsValid}"); // Should be true
    }

    public void TestPositionManagement()
    {
        // Arrange
        var position = new Position
        {
            Strategy = "IronCondor",
            UnderlyingSymbol = "SPY",
            OpenCredit = 200m,
            CurrentValue = -100m,
            ProfitTarget = 100m,
            StopLoss = 160m,
            ExpirationDate = DateTime.Today,
            Status = PositionStatus.Open
        };

        // Act
        position.UnrealizedPnL = position.OpenCredit + position.CurrentValue;

        // Assert
        Console.WriteLine($"Unrealized P&L: {position.UnrealizedPnL:C}"); // Should be $100
        Console.WriteLine($"P&L Percentage: {position.PnLPercentage:F1}%"); // Should be 50%
        Console.WriteLine($"Should Close: {position.ShouldClose}"); // Should be true (profit target reached)
    }

    public static void RunAllTests()
    {
        var tests = new BasicTests();
        
        Console.WriteLine("=== Running Basic Tests ===\n");
        
        Console.WriteLine("1. Testing Option Contract Properties:");
        tests.TestOptionContractProperties();
        
        Console.WriteLine("\n2. Testing Trading Signal Validation:");
        tests.TestTradingSignalValidation();
        
        Console.WriteLine("\n3. Testing Position Management:");
        tests.TestPositionManagement();
        
        Console.WriteLine("\n=== All Tests Completed ===");
    }
}
