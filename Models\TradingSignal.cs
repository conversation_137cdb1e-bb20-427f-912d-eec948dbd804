namespace ZeroDateStrat.Models;

public class TradingSignal
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string Strategy { get; set; } = string.Empty;
    public string UnderlyingSymbol { get; set; } = string.Empty;
    public SignalType Type { get; set; }
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    public DateTime ExpirationDate { get; set; }
    public decimal Confidence { get; set; }
    public decimal ExpectedProfit { get; set; }
    public decimal MaxLoss { get; set; }
    public List<OptionLeg> Legs { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
    public string Reason { get; set; } = string.Empty;
    public bool IsExecuted { get; set; }
    public DateTime? ExecutedAt { get; set; }

    public decimal RiskRewardRatio => MaxLoss != 0 ? ExpectedProfit / Math.Abs(MaxLoss) : 0;
    public bool IsValid => Legs.Any() && Confidence > 0.5m && RiskRewardRatio > 0.3m;
}

public class OptionLeg
{
    public string Symbol { get; set; } = string.Empty;
    public OptionType OptionType { get; set; }
    public decimal StrikePrice { get; set; }
    public DateTime ExpirationDate { get; set; }
    public OrderSide Side { get; set; }
    public int Quantity { get; set; }
    public decimal Price { get; set; }
    public decimal Delta { get; set; }
    public decimal Gamma { get; set; }
    public decimal Theta { get; set; }
    public decimal Vega { get; set; }
}

public enum SignalType
{
    IronCondor,
    IronButterfly,
    Strangle,
    Straddle,
    CallSpread,
    PutSpread,
    SingleOption
}

public enum OrderSide
{
    Buy,
    Sell
}

public class Position
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string Strategy { get; set; } = string.Empty;
    public string UnderlyingSymbol { get; set; } = string.Empty;
    public List<OptionLeg> Legs { get; set; } = new();
    public decimal OpenCredit { get; set; }
    public decimal CurrentValue { get; set; }
    public decimal UnrealizedPnL { get; set; }
    public DateTime OpenedAt { get; set; }
    public DateTime? ClosedAt { get; set; }
    public PositionStatus Status { get; set; } = PositionStatus.Open;
    public decimal ProfitTarget { get; set; }
    public decimal StopLoss { get; set; }
    public DateTime ExpirationDate { get; set; }

    public decimal PnLPercentage => OpenCredit != 0 ? (UnrealizedPnL / OpenCredit) * 100 : 0;
    public bool ShouldClose => Status == PositionStatus.Open && 
                              (UnrealizedPnL >= ProfitTarget || 
                               UnrealizedPnL <= -StopLoss ||
                               DateTime.UtcNow.Date >= ExpirationDate.Date);
}

public enum PositionStatus
{
    Open,
    Closed,
    Expired
}
