using Alpaca.Markets;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

public interface IAlpacaService
{
    Task<bool> InitializeAsync();
    Task<IAccount?> GetAccountAsync();
    Task<List<OptionContract>> GetOptionChainAsync(string symbol, DateTime expirationDate);
    Task<decimal> GetCurrentPriceAsync(string symbol);
    Task<IOrder?> PlaceOrderAsync(TradingSignal signal);
    Task<List<IPosition>> GetPositionsAsync();
    Task<bool> ClosePositionAsync(string positionId);
    Task<List<IOrder>> GetOrdersAsync();
}

public class AlpacaService : IAlpacaService, IDisposable
{
    private readonly ILogger<AlpacaService> _logger;
    private readonly IConfiguration _configuration;
    private IAlpacaTradingClient? _tradingClient;
    private IAlpacaDataClient? _dataClient;
    private IAlpacaOptionsDataClient? _optionsDataClient;
    private bool _isInitialized;

    public AlpacaService(ILogger<AlpacaService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
    }

    public async Task<bool> InitializeAsync()
    {
        try
        {
            var apiKey = _configuration["Alpaca:ApiKey"];
            var secretKey = _configuration["Alpaca:SecretKey"];
            var baseUrl = _configuration["Alpaca:BaseUrl"];
            var dataUrl = _configuration["Alpaca:DataUrl"];

            if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(secretKey))
            {
                _logger.LogError("Alpaca API credentials not configured");
                return false;
            }

            var tradingClientConfiguration = new AlpacaTradingClientConfiguration
            {
                ApiEndpoint = new Uri(baseUrl ?? "https://paper-api.alpaca.markets"),
                SecurityId = new SecretKey(apiKey, secretKey)
            };

            var dataClientConfiguration = new AlpacaDataClientConfiguration
            {
                ApiEndpoint = new Uri(dataUrl ?? "https://data.alpaca.markets"),
                SecurityId = new SecretKey(apiKey, secretKey)
            };

            _tradingClient = Environments.Paper.GetAlpacaTradingClient(tradingClientConfiguration);
            _dataClient = Environments.Paper.GetAlpacaDataClient(dataClientConfiguration);
            _optionsDataClient = Environments.Paper.GetAlpacaOptionsDataClient(dataClientConfiguration);

            // Test connection
            var account = await _tradingClient.GetAccountAsync();
            _logger.LogInformation($"Connected to Alpaca. Account: {account.AccountNumber}");

            _isInitialized = true;
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize Alpaca service");
            return false;
        }
    }

    public async Task<IAccount?> GetAccountAsync()
    {
        if (!_isInitialized || _tradingClient == null)
            return null;

        try
        {
            return await _tradingClient.GetAccountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get account information");
            return null;
        }
    }

    public async Task<List<OptionContract>> GetOptionChainAsync(string symbol, DateTime expirationDate)
    {
        if (!_isInitialized || _optionsDataClient == null)
            return new List<OptionContract>();

        try
        {
            var request = new OptionChainRequest(symbol)
            {
                ExpirationDateGreaterThanOrEqualTo = expirationDate.Date,
                ExpirationDateLessThanOrEqualTo = expirationDate.Date
            };

            var response = await _optionsDataClient.GetOptionChainAsync(request);
            var contracts = new List<OptionContract>();

            foreach (var option in response.Items)
            {
                contracts.Add(new OptionContract
                {
                    Symbol = option.Symbol,
                    UnderlyingSymbol = symbol,
                    ExpirationDate = option.ExpirationDate,
                    StrikePrice = option.StrikePrice,
                    OptionType = option.OptionStyle == OptionStyle.Call ? OptionType.Call : OptionType.Put,
                    LastUpdated = DateTime.UtcNow
                });
            }

            return contracts;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to get option chain for {symbol}");
            return new List<OptionContract>();
        }
    }

    public async Task<decimal> GetCurrentPriceAsync(string symbol)
    {
        if (!_isInitialized || _dataClient == null)
            return 0;

        try
        {
            var request = new LatestMarketDataRequest(new[] { symbol });
            var response = await _dataClient.GetLatestTradeAsync(request);
            
            if (response.Items.TryGetValue(symbol, out var trade))
            {
                return trade.Price;
            }

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to get current price for {symbol}");
            return 0;
        }
    }

    public async Task<IOrder?> PlaceOrderAsync(TradingSignal signal)
    {
        if (!_isInitialized || _tradingClient == null)
            return null;

        try
        {
            // For now, implement a simple single leg order
            // In a real implementation, you'd handle multi-leg orders
            var firstLeg = signal.Legs.FirstOrDefault();
            if (firstLeg == null)
                return null;

            var orderRequest = new NewOrderRequest(
                firstLeg.Symbol,
                firstLeg.Quantity,
                firstLeg.Side == OrderSide.Buy ? Alpaca.Markets.OrderSide.Buy : Alpaca.Markets.OrderSide.Sell,
                OrderType.Limit,
                TimeInForce.Day)
            {
                LimitPrice = firstLeg.Price
            };

            return await _tradingClient.PostOrderAsync(orderRequest);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to place order");
            return null;
        }
    }

    public async Task<List<IPosition>> GetPositionsAsync()
    {
        if (!_isInitialized || _tradingClient == null)
            return new List<IPosition>();

        try
        {
            var positions = await _tradingClient.ListPositionsAsync();
            return positions.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get positions");
            return new List<IPosition>();
        }
    }

    public async Task<bool> ClosePositionAsync(string positionId)
    {
        if (!_isInitialized || _tradingClient == null)
            return false;

        try
        {
            await _tradingClient.DeletePositionAsync(positionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to close position {positionId}");
            return false;
        }
    }

    public async Task<List<IOrder>> GetOrdersAsync()
    {
        if (!_isInitialized || _tradingClient == null)
            return new List<IOrder>();

        try
        {
            var orders = await _tradingClient.ListOrdersAsync(new ListOrdersRequest());
            return orders.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get orders");
            return new List<IOrder>();
        }
    }

    public void Dispose()
    {
        _tradingClient?.Dispose();
        _dataClient?.Dispose();
        _optionsDataClient?.Dispose();
    }
}
